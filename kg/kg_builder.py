#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱构建核心模块
连接Neo4j数据库，创建Person和Profession节点，建立HAS_PROFESSION关系
"""

import logging
from typing import Dict, List, Any, Optional
import pandas as pd
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, AuthError
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KnowledgeGraphBuilder:
    """知识图谱构建器"""
    
    def __init__(self, uri: str = "bolt://localhost:7688", 
                 username: str = "neo4j", 
                 password: str = "kg_password_123",
                 database: str = "neo4j"):
        """
        初始化知识图谱构建器
        
        Args:
            uri: Neo4j连接URI
            username: 用户名
            password: 密码
            database: 数据库名称
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.driver = None
        
    def connect(self) -> bool:
        """
        连接到Neo4j数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"正在连接到Neo4j数据库: {self.uri}")
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.username, self.password)
            )
            
            # 测试连接
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    logger.info("Neo4j连接成功!")
                    return True
                    
        except ServiceUnavailable as e:
            logger.error(f"Neo4j服务不可用: {e}")
            logger.error("请确保Neo4j容器正在运行: docker-compose up -d")
        except AuthError as e:
            logger.error(f"认证失败: {e}")
            logger.error("请检查用户名和密码是否正确")
        except Exception as e:
            logger.error(f"连接失败: {e}")
            
        return False
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logger.info("数据库连接已关闭")
    
    def clear_database(self) -> bool:
        """
        清空数据库（谨慎使用）
        
        Returns:
            bool: 操作是否成功
        """
        if not self.driver:
            logger.error("请先连接数据库")
            return False
            
        try:
            with self.driver.session(database=self.database) as session:
                # 删除所有节点和关系
                session.run("MATCH (n) DETACH DELETE n")
                logger.info("数据库已清空")
                return True
                
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """
        创建索引以提高查询性能
        
        Returns:
            bool: 操作是否成功
        """
        if not self.driver:
            logger.error("请先连接数据库")
            return False
            
        try:
            with self.driver.session(database=self.database) as session:
                # 为Person节点的nconst属性创建索引
                try:
                    session.run("CREATE INDEX person_nconst_index IF NOT EXISTS FOR (p:Person) ON (p.nconst)")
                    logger.info("Person.nconst索引创建成功")
                except Exception as e:
                    logger.warning(f"Person.nconst索引可能已存在: {e}")
                
                # 为Person节点的primaryName属性创建索引
                try:
                    session.run("CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.primaryName)")
                    logger.info("Person.primaryName索引创建成功")
                except Exception as e:
                    logger.warning(f"Person.primaryName索引可能已存在: {e}")
                
                # 为Profession节点的name属性创建索引
                try:
                    session.run("CREATE INDEX profession_name_index IF NOT EXISTS FOR (prof:Profession) ON (prof.name)")
                    logger.info("Profession.name索引创建成功")
                except Exception as e:
                    logger.warning(f"Profession.name索引可能已存在: {e}")
                
                return True
                
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            return False
    
    def create_person_node(self, tx, person_data: Dict[str, Any]):
        """
        创建Person节点的事务函数
        
        Args:
            tx: Neo4j事务对象
            person_data: 人员数据字典
        """
        query = """
        MERGE (p:Person {nconst: $nconst})
        ON CREATE SET 
            p.primaryName = $primaryName, 
            p.birthYear = $birthYear,
            p.created_at = datetime()
        ON MATCH SET 
            p.updated_at = datetime()
        RETURN p
        """
        
        result = tx.run(query, 
                       nconst=person_data['nconst'],
                       primaryName=person_data['primaryName'],
                       birthYear=person_data['birthYear'])
        return result.single()
    
    def create_profession_and_relationship(self, tx, person_nconst: str, profession_name: str):
        """
        创建Profession节点并建立与Person的关系
        
        Args:
            tx: Neo4j事务对象
            person_nconst: 人员ID
            profession_name: 职业名称
        """
        query = """
        MERGE (prof:Profession {name: $profession_name})
        ON CREATE SET prof.created_at = datetime()
        WITH prof
        MATCH (p:Person {nconst: $person_nconst})
        MERGE (p)-[r:HAS_PROFESSION]->(prof)
        ON CREATE SET r.created_at = datetime()
        RETURN p, r, prof
        """
        
        result = tx.run(query, 
                       person_nconst=person_nconst,
                       profession_name=profession_name)
        return result.single()
    
    def build_knowledge_graph(self, processed_data: pd.DataFrame, batch_size: int = 100) -> bool:
        """
        构建知识图谱
        
        Args:
            processed_data: 预处理后的数据
            batch_size: 批处理大小
            
        Returns:
            bool: 构建是否成功
        """
        if not self.driver:
            logger.error("请先连接数据库")
            return False
            
        if processed_data is None or processed_data.empty:
            logger.error("没有数据可处理")
            return False
            
        try:
            logger.info(f"开始构建知识图谱，共 {len(processed_data)} 条记录")
            
            # 创建索引
            self.create_indexes()
            
            total_records = len(processed_data)
            processed_count = 0
            
            # 分批处理数据
            for start_idx in range(0, total_records, batch_size):
                end_idx = min(start_idx + batch_size, total_records)
                batch_data = processed_data.iloc[start_idx:end_idx]
                
                logger.info(f"处理批次 {start_idx//batch_size + 1}: 记录 {start_idx+1}-{end_idx}")
                
                with self.driver.session(database=self.database) as session:
                    for _, row in batch_data.iterrows():
                        try:
                            # 创建Person节点
                            person_data = {
                                'nconst': row['nconst'],
                                'primaryName': row['primaryName'],
                                'birthYear': int(row['birthYear'])
                            }
                            
                            session.execute_write(self.create_person_node, person_data)
                            
                            # 为每个职业创建节点和关系
                            for profession in row['professions']:
                                session.execute_write(
                                    self.create_profession_and_relationship,
                                    row['nconst'],
                                    profession
                                )
                            
                            processed_count += 1
                            
                        except Exception as e:
                            logger.error(f"处理记录失败 {row['nconst']}: {e}")
                            continue
                
                # 显示进度
                progress = (processed_count / total_records) * 100
                logger.info(f"进度: {processed_count}/{total_records} ({progress:.1f}%)")
            
            logger.info(f"知识图谱构建完成! 成功处理 {processed_count} 条记录")
            
            # 显示构建结果统计
            self.show_graph_statistics()
            
            return True
            
        except Exception as e:
            logger.error(f"构建知识图谱失败: {e}")
            return False
    
    def show_graph_statistics(self):
        """显示图谱统计信息"""
        if not self.driver:
            logger.error("请先连接数据库")
            return
            
        try:
            with self.driver.session(database=self.database) as session:
                # 统计Person节点数量
                result = session.run("MATCH (p:Person) RETURN count(p) as person_count")
                person_count = result.single()["person_count"]
                
                # 统计Profession节点数量
                result = session.run("MATCH (prof:Profession) RETURN count(prof) as profession_count")
                profession_count = result.single()["profession_count"]
                
                # 统计关系数量
                result = session.run("MATCH ()-[r:HAS_PROFESSION]->() RETURN count(r) as relationship_count")
                relationship_count = result.single()["relationship_count"]
                
                # 统计热门职业
                result = session.run("""
                    MATCH (prof:Profession)<-[:HAS_PROFESSION]-()
                    RETURN prof.name as profession, count(*) as count
                    ORDER BY count DESC
                    LIMIT 10
                """)
                top_professions = [(record["profession"], record["count"]) for record in result]
                
                logger.info("=== 知识图谱统计信息 ===")
                logger.info(f"Person节点数量: {person_count}")
                logger.info(f"Profession节点数量: {profession_count}")
                logger.info(f"HAS_PROFESSION关系数量: {relationship_count}")
                logger.info("热门职业 (前10):")
                for profession, count in top_professions:
                    logger.info(f"  {profession}: {count}")
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
    
    def test_connection(self) -> bool:
        """
        测试数据库连接和基本查询
        
        Returns:
            bool: 测试是否成功
        """
        if not self.driver:
            logger.error("请先连接数据库")
            return False
            
        try:
            with self.driver.session(database=self.database) as session:
                # 测试基本查询
                result = session.run("MATCH (p:Person) RETURN p.primaryName as name LIMIT 5")
                names = [record["name"] for record in result]
                
                if names:
                    logger.info("数据库连接测试成功!")
                    logger.info(f"示例人员: {', '.join(names)}")
                    return True
                else:
                    logger.warning("数据库连接正常，但没有找到Person节点")
                    return True
                    
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False


def main():
    """主函数，演示知识图谱构建流程"""
    logger.info("=== 知识图谱构建模块测试 ===")
    
    # 首先需要启动Neo4j容器
    logger.info("请确保Neo4j容器正在运行:")
    logger.info("cd /Users/<USER>/work/python/deepai/kg && ./start_neo4j.sh")
    
    # 初始化构建器
    builder = KnowledgeGraphBuilder()
    
    try:
        # 连接数据库
        if not builder.connect():
            logger.error("无法连接到数据库，请检查Neo4j是否正在运行")
            return
        
        # 测试连接
        builder.test_connection()
        
        # 加载预处理的数据
        from data_processor import DataProcessor
        
        processor = DataProcessor("sample_name.basics.tsv")  # 使用示例数据
        if not processor.load_data():
            logger.error("数据加载失败")
            return
            
        if not processor.clean_data():
            logger.error("数据清洗失败")
            return
            
        if not processor.process_professions():
            logger.error("职业处理失败")
            return
        
        processed_data = processor.get_processed_data()
        
        # 构建知识图谱
        if builder.build_knowledge_graph(processed_data):
            logger.info("知识图谱构建成功!")
        else:
            logger.error("知识图谱构建失败")
            
    finally:
        # 关闭连接
        builder.close()


if __name__ == "__main__":
    main()
