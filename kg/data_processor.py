#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理模块
处理 IMDb name.basics.tsv 数据文件，进行数据清洗和预处理
"""

import pandas as pd
import logging
from typing import List, Dict, Any, Optional
import os
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataProcessor:
    """数据预处理器"""
    
    def __init__(self, data_file: str = "name.basics.tsv"):
        """
        初始化数据处理器
        
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.df = None
        self.processed_df = None
        
    def load_data(self) -> bool:
        """
        加载TSV数据文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.data_file):
                logger.warning(f"数据文件 {self.data_file} 不存在，将创建示例数据")
                self._create_sample_data()
                return True
                
            logger.info(f"正在加载数据文件: {self.data_file}")
            
            # 读取TSV文件，使用制表符作为分隔符
            self.df = pd.read_csv(
                self.data_file, 
                sep='\t', 
                low_memory=False,
                na_values=['\\N'],  # IMDb使用\N表示缺失值
                dtype={
                    'nconst': 'string',
                    'primaryName': 'string',
                    'birthYear': 'string',
                    'deathYear': 'string',
                    'primaryProfession': 'string',
                    'knownForTitles': 'string'
                }
            )
            
            logger.info(f"数据加载完成，共 {len(self.df)} 条记录")
            logger.info(f"数据列: {list(self.df.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            return False
    
    def _create_sample_data(self):
        """创建示例数据用于测试"""
        logger.info("创建示例数据...")
        
        sample_data = [
            ['nm0000001', 'Tom Hanks', '1956', '\\N', 'actor,producer,director', 'tt0109830,tt0120815,tt0317219,tt0120363'],
            ['nm0000002', 'Morgan Freeman', '1937', '\\N', 'actor,producer,director', 'tt0111161,tt0317219,tt0120915,tt0114814'],
            ['nm0000003', 'Robert De Niro', '1943', '\\N', 'actor,producer,director', 'tt0099685,tt0075314,tt0080656,tt0073486'],
            ['nm0000004', 'Meryl Streep', '1949', '\\N', 'actress,producer', 'tt0077405,tt0082971,tt0110357,tt1504320'],
            ['nm0000005', 'Leonardo DiCaprio', '1974', '\\N', 'actor,producer', 'tt0110912,tt0120338,tt0993846,tt1375666'],
            ['nm0000006', 'Brad Pitt', '1963', '\\N', 'actor,producer', 'tt0137523,tt0361748,tt0434409,tt1375666'],
            ['nm0000007', 'Angelina Jolie', '1975', '\\N', 'actress,producer,director', 'tt0120338,tt0454876,tt0258463,tt0936501'],
            ['nm0000008', 'Will Smith', '1968', '\\N', 'actor,producer,soundtrack', 'tt0120382,tt0119654,tt0386588,tt0454921'],
            ['nm0000009', 'Johnny Depp', '1963', '\\N', 'actor,producer,director', 'tt0325980,tt0383574,tt0172495,tt0099685'],
            ['nm0000010', 'Scarlett Johansson', '1984', '\\N', 'actress,producer,soundtrack', 'tt0335266,tt0848228,tt1843866,tt4154756']
        ]
        
        columns = ['nconst', 'primaryName', 'birthYear', 'deathYear', 'primaryProfession', 'knownForTitles']
        self.df = pd.DataFrame(sample_data, columns=columns)
        
        # 保存示例数据到文件
        sample_file = "sample_name.basics.tsv"
        self.df.to_csv(sample_file, sep='\t', index=False, na_rep='\\N')
        logger.info(f"示例数据已保存到: {sample_file}")
    
    def clean_data(self) -> bool:
        """
        数据清洗
        
        Returns:
            bool: 清洗是否成功
        """
        if self.df is None:
            logger.error("请先加载数据")
            return False
            
        try:
            logger.info("开始数据清洗...")
            
            # 记录原始数据量
            original_count = len(self.df)
            logger.info(f"原始数据量: {original_count}")
            
            # 1. 删除关键字段为空的记录
            self.df = self.df.dropna(subset=['primaryName', 'primaryProfession'])
            logger.info(f"删除姓名或职业为空的记录后: {len(self.df)} 条")
            
            # 2. 处理出生年份
            # 将 '\\N' 替换为 NaN，然后转换为数值类型
            self.df['birthYear'] = self.df['birthYear'].replace('\\N', pd.NA)
            self.df = self.df.dropna(subset=['birthYear'])
            self.df['birthYear'] = pd.to_numeric(self.df['birthYear'], errors='coerce')
            self.df = self.df.dropna(subset=['birthYear'])
            self.df['birthYear'] = self.df['birthYear'].astype(int)
            logger.info(f"处理出生年份后: {len(self.df)} 条")
            
            # 3. 过滤合理的出生年份范围 (1800-2020)
            self.df = self.df[
                (self.df['birthYear'] >= 1800) & 
                (self.df['birthYear'] <= 2020)
            ]
            logger.info(f"过滤出生年份范围后: {len(self.df)} 条")
            
            # 4. 清理职业字段
            self.df['primaryProfession'] = self.df['primaryProfession'].str.strip()
            self.df = self.df[self.df['primaryProfession'] != '']
            logger.info(f"清理职业字段后: {len(self.df)} 条")
            
            # 5. 只保留需要的列
            required_columns = ['nconst', 'primaryName', 'birthYear', 'primaryProfession']
            self.df = self.df[required_columns]
            
            logger.info(f"数据清洗完成，保留 {len(self.df)} 条记录 (原始: {original_count})")
            
            return True
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return False
    
    def process_professions(self) -> bool:
        """
        处理职业字段，拆分多个职业
        
        Returns:
            bool: 处理是否成功
        """
        if self.df is None:
            logger.error("请先加载和清洗数据")
            return False
            
        try:
            logger.info("开始处理职业字段...")
            
            # 拆分职业字符串
            # "actor,producer,soundtrack" -> ["actor", "producer", "soundtrack"]
            self.df['professions'] = self.df['primaryProfession'].str.split(',')
            
            # 清理职业名称（去除空格，统一大小写）
            self.df['professions'] = self.df['professions'].apply(
                lambda x: [prof.strip().lower() for prof in x if prof.strip()]
            )
            
            # 过滤掉空的职业列表
            self.df = self.df[self.df['professions'].apply(len) > 0]
            
            # 统计职业分布
            all_professions = []
            for prof_list in self.df['professions']:
                all_professions.extend(prof_list)
            
            profession_counts = pd.Series(all_professions).value_counts()
            logger.info(f"职业分布统计 (前10):")
            for prof, count in profession_counts.head(10).items():
                logger.info(f"  {prof}: {count}")
            
            self.processed_df = self.df.copy()
            logger.info(f"职业处理完成，共 {len(self.processed_df)} 条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"职业处理失败: {e}")
            return False
    
    def get_processed_data(self) -> Optional[pd.DataFrame]:
        """
        获取处理后的数据
        
        Returns:
            处理后的DataFrame，如果未处理则返回None
        """
        return self.processed_df
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Returns:
            统计信息字典
        """
        if self.processed_df is None:
            return {}
            
        stats = {
            'total_records': len(self.processed_df),
            'unique_persons': self.processed_df['nconst'].nunique(),
            'birth_year_range': {
                'min': int(self.processed_df['birthYear'].min()),
                'max': int(self.processed_df['birthYear'].max())
            },
            'profession_stats': {}
        }
        
        # 统计职业信息
        all_professions = []
        for prof_list in self.processed_df['professions']:
            all_professions.extend(prof_list)
        
        profession_counts = pd.Series(all_professions).value_counts()
        stats['profession_stats'] = {
            'total_professions': len(profession_counts),
            'top_professions': profession_counts.head(10).to_dict()
        }
        
        return stats
    
    def save_processed_data(self, output_file: str = "processed_name_basics.tsv") -> bool:
        """
        保存处理后的数据
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        if self.processed_df is None:
            logger.error("没有处理后的数据可保存")
            return False
            
        try:
            # 为了保存，需要将professions列表转换回字符串
            save_df = self.processed_df.copy()
            save_df['professions_str'] = save_df['professions'].apply(lambda x: ','.join(x))
            
            # 保存处理后的数据
            save_df[['nconst', 'primaryName', 'birthYear', 'professions_str']].to_csv(
                output_file, sep='\t', index=False
            )
            
            logger.info(f"处理后的数据已保存到: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False


def main():
    """主函数，演示数据处理流程"""
    logger.info("=== 数据预处理模块测试 ===")
    
    # 初始化数据处理器
    processor = DataProcessor("name.basics.tsv")
    
    # 加载数据
    if not processor.load_data():
        logger.error("数据加载失败")
        return
    
    # 清洗数据
    if not processor.clean_data():
        logger.error("数据清洗失败")
        return
    
    # 处理职业字段
    if not processor.process_professions():
        logger.error("职业处理失败")
        return
    
    # 获取统计信息
    stats = processor.get_statistics()
    logger.info("=== 数据统计信息 ===")
    logger.info(f"总记录数: {stats['total_records']}")
    logger.info(f"唯一人员数: {stats['unique_persons']}")
    logger.info(f"出生年份范围: {stats['birth_year_range']['min']} - {stats['birth_year_range']['max']}")
    logger.info(f"职业类型数: {stats['profession_stats']['total_professions']}")
    logger.info("热门职业:")
    for prof, count in stats['profession_stats']['top_professions'].items():
        logger.info(f"  {prof}: {count}")
    
    # 保存处理后的数据
    processor.save_processed_data()
    
    # 显示示例数据
    processed_data = processor.get_processed_data()
    logger.info("=== 处理后数据示例 ===")
    logger.info(processed_data.head().to_string())


if __name__ == "__main__":
    main()
