# RAG个人知识库 - 简化版依赖

# 核心框架
langchain>=0.2.0
langchain-community>=0.2.0
langchain-core>=0.2.0
langchain-text-splitters>=0.2.0

# 深度学习框架
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.0

# 重排序模型支持
# 注意：这些模型需要从Hugging Face下载，确保网络连接正常
# 双编码器模型：BAAI/bge-reranker-base
# 交叉编码器模型：BAAI/bge-reranker-v2-m3

# 向量数据库
chromadb>=0.4.0
psycopg2-binary>=2.9.0  # PostgreSQL数据库驱动
pgvector>=0.2.0  # PostgreSQL向量扩展

# 数据处理
pydantic>=2.0.0
numpy>=1.24.0

# 通义百炼API
dashscope>=1.14.0

# Redis会话存储
redis>=4.5.0

# 中文处理
jieba>=0.42.1

# BM25检索
rank-bm25>=0.2.2

# 工具库
loguru>=0.7.0
rich>=13.0.0
click>=8.1.0

# 开发工具
pytest>=7.4.0

elasticsearch>=8.18.2
pymysql
sqlalchemy