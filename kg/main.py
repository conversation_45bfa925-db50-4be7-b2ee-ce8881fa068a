#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱项目主程序
整合所有模块，提供统一的命令行接口
"""

import argparse
import logging
import sys
import json
from pathlib import Path
from typing import Optional

from data_processor import DataProcessor
from kg_builder import KnowledgeGraphBuilder
from query_interface import KGQueryInterface
from llm_integration import KGLLMInterface

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KnowledgeGraphSystem:
    """知识图谱系统主类"""
    
    def __init__(self, 
                 data_file: str = "name.basics.tsv",
                 neo4j_uri: str = "bolt://localhost:7688",
                 neo4j_username: str = "neo4j",
                 neo4j_password: str = "kg_password_123"):
        """
        初始化知识图谱系统
        
        Args:
            data_file: 数据文件路径
            neo4j_uri: Neo4j连接URI
            neo4j_username: Neo4j用户名
            neo4j_password: Neo4j密码
        """
        self.data_file = data_file
        self.neo4j_uri = neo4j_uri
        self.neo4j_username = neo4j_username
        self.neo4j_password = neo4j_password
        
        # 初始化各个模块
        self.data_processor = DataProcessor(data_file)
        self.kg_builder = KnowledgeGraphBuilder(neo4j_uri, neo4j_username, neo4j_password)
        self.query_interface = KGQueryInterface(neo4j_uri, neo4j_username, neo4j_password)
        self.llm_interface = KGLLMInterface(
            neo4j_uri=neo4j_uri,
            neo4j_username=neo4j_username,
            neo4j_password=neo4j_password
        )
        
        logger.info("知识图谱系统初始化完成")
    
    def check_environment(self) -> bool:
        """
        检查环境和依赖
        
        Returns:
            bool: 环境检查是否通过
        """
        logger.info("=== 环境检查 ===")
        
        checks = []
        
        # 检查数据文件
        if Path(self.data_file).exists():
            checks.append(("数据文件", True, f"找到数据文件: {self.data_file}"))
        else:
            checks.append(("数据文件", False, f"数据文件不存在: {self.data_file}，将使用示例数据"))
        
        # 检查Neo4j连接
        try:
            if self.kg_builder.connect():
                checks.append(("Neo4j连接", True, "Neo4j数据库连接成功"))
                self.kg_builder.close()
            else:
                checks.append(("Neo4j连接", False, "无法连接到Neo4j数据库"))
        except Exception as e:
            checks.append(("Neo4j连接", False, f"Neo4j连接异常: {e}"))
        
        # 检查Python依赖
        try:
            import pandas
            import neo4j
            import dashscope
            checks.append(("Python依赖", True, "所有必需的Python包已安装"))
        except ImportError as e:
            checks.append(("Python依赖", False, f"缺少依赖包: {e}"))
        
        # 输出检查结果
        success_count = sum(1 for _, success, _ in checks if success)
        total_count = len(checks)
        
        for name, success, message in checks:
            status = "✅" if success else "❌"
            logger.info(f"{status} {name}: {message}")
        
        logger.info(f"环境检查完成: {success_count}/{total_count} 项通过")
        
        return success_count == total_count
    
    def build_knowledge_graph(self, clear_existing: bool = False) -> bool:
        """
        构建知识图谱
        
        Args:
            clear_existing: 是否清空现有数据
            
        Returns:
            bool: 构建是否成功
        """
        logger.info("=== 构建知识图谱 ===")
        
        try:
            # 1. 数据预处理
            logger.info("1. 开始数据预处理...")
            if not self.data_processor.load_data():
                logger.error("数据加载失败")
                return False
            
            if not self.data_processor.clean_data():
                logger.error("数据清洗失败")
                return False
            
            if not self.data_processor.process_professions():
                logger.error("职业处理失败")
                return False
            
            processed_data = self.data_processor.get_processed_data()
            stats = self.data_processor.get_statistics()
            logger.info(f"数据预处理完成: {stats['total_records']} 条记录")
            
            # 2. 连接数据库
            logger.info("2. 连接Neo4j数据库...")
            if not self.kg_builder.connect():
                logger.error("无法连接到Neo4j数据库")
                return False
            
            # 3. 清空现有数据（如果需要）
            if clear_existing:
                logger.info("3. 清空现有数据...")
                self.kg_builder.clear_database()
            
            # 4. 构建知识图谱
            logger.info("4. 构建知识图谱...")
            if not self.kg_builder.build_knowledge_graph(processed_data):
                logger.error("知识图谱构建失败")
                return False
            
            logger.info("✅ 知识图谱构建成功!")
            return True
            
        except Exception as e:
            logger.error(f"构建知识图谱异常: {e}")
            return False
        finally:
            self.kg_builder.close()
    
    def test_queries(self) -> bool:
        """
        测试查询功能
        
        Returns:
            bool: 测试是否通过
        """
        logger.info("=== 测试查询功能 ===")
        
        try:
            # 连接数据库
            if not self.query_interface.connect():
                logger.error("无法连接到数据库")
                return False
            
            # 测试基础查询
            test_cases = [
                ("查询Tom Hanks的职业", lambda: self.query_interface.get_person_professions("Tom Hanks")),
                ("查询所有演员", lambda: self.query_interface.get_people_by_profession("actor", 5)),
                ("查询既是演员又是导演的人", lambda: self.query_interface.get_people_with_multiple_professions(["actor", "director"], 5)),
                ("获取职业统计", lambda: self.query_interface.get_profession_statistics()),
                ("搜索包含Tom的人物", lambda: self.query_interface.search_people_by_name_pattern("Tom", 3))
            ]
            
            success_count = 0
            for test_name, test_func in test_cases:
                try:
                    logger.info(f"测试: {test_name}")
                    result = test_func()
                    if result.get("success", False) or "results" in result:
                        logger.info(f"✅ {test_name} - 通过")
                        success_count += 1
                    else:
                        logger.warning(f"⚠️ {test_name} - 无结果: {result.get('message', '未知')}")
                except Exception as e:
                    logger.error(f"❌ {test_name} - 失败: {e}")
            
            logger.info(f"查询测试完成: {success_count}/{len(test_cases)} 项通过")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"查询测试异常: {e}")
            return False
        finally:
            self.query_interface.close()
    
    def test_llm_integration(self) -> bool:
        """
        测试LLM集成功能
        
        Returns:
            bool: 测试是否通过
        """
        logger.info("=== 测试LLM集成功能 ===")
        
        try:
            # 连接数据库
            if not self.llm_interface.connect():
                logger.error("无法连接到数据库")
                return False
            
            # 测试自然语言查询
            test_questions = [
                "Tom Hanks有哪些职业？",
                "谁是演员？",
                "哪些人既是演员又是导演？"
            ]
            
            success_count = 0
            for question in test_questions:
                try:
                    logger.info(f"测试问题: {question}")
                    result = self.llm_interface.natural_language_query(question)
                    if result.get("success", False):
                        logger.info(f"✅ 回答: {result['answer']}")
                        success_count += 1
                    else:
                        logger.warning(f"⚠️ 查询失败: {result.get('error', '未知错误')}")
                except Exception as e:
                    logger.error(f"❌ 查询异常: {e}")
            
            logger.info(f"LLM测试完成: {success_count}/{len(test_questions)} 项通过")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"LLM测试异常: {e}")
            return False
        finally:
            self.llm_interface.close()
    
    def interactive_query(self):
        """交互式查询模式"""
        logger.info("=== 交互式查询模式 ===")
        print("欢迎使用知识图谱问答系统!")
        print("输入 'quit' 或 'exit' 退出")
        print("输入 'help' 查看帮助")
        print("-" * 50)
        
        try:
            # 连接数据库
            if not self.llm_interface.connect():
                print("❌ 无法连接到数据库，请检查Neo4j是否正在运行")
                return
            
            while True:
                try:
                    user_input = input("\n🤔 请输入您的问题: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', '退出']:
                        break
                    elif user_input.lower() == 'help':
                        print("""
可以询问的问题类型:
1. 查询特定人物的职业: "Tom Hanks有哪些职业？"
2. 查询特定职业的从业者: "谁是演员？"
3. 查询多重身份的人: "哪些人既是演员又是导演？"
4. 搜索人物: "有哪些叫Tom的人？"
5. 统计信息: "有多少种职业？"
                        """)
                        continue
                    elif not user_input:
                        continue
                    
                    print("\n🤖 正在思考...")
                    result = self.llm_interface.natural_language_query(user_input)
                    
                    if result.get("success", False):
                        print(f"💡 回答: {result['answer']}")
                        if result.get("cypher_query"):
                            print(f"🔍 生成的查询: {result['cypher_query']}")
                    else:
                        print(f"❌ 抱歉，查询失败: {result.get('error', '未知错误')}")
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"❌ 发生错误: {e}")
            
            print("\n👋 感谢使用，再见!")
            
        except Exception as e:
            logger.error(f"交互式查询异常: {e}")
        finally:
            self.llm_interface.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="知识图谱项目")
    parser.add_argument("command", choices=["check", "build", "test", "query", "interactive"], 
                       help="执行的命令")
    parser.add_argument("--data-file", default="name.basics.tsv", help="数据文件路径")
    parser.add_argument("--clear", action="store_true", help="清空现有数据")
    parser.add_argument("--neo4j-uri", default="bolt://localhost:7688", help="Neo4j连接URI")
    parser.add_argument("--neo4j-user", default="neo4j", help="Neo4j用户名")
    parser.add_argument("--neo4j-password", default="kg_password_123", help="Neo4j密码")
    
    args = parser.parse_args()
    
    # 初始化系统
    kg_system = KnowledgeGraphSystem(
        data_file=args.data_file,
        neo4j_uri=args.neo4j_uri,
        neo4j_username=args.neo4j_user,
        neo4j_password=args.neo4j_password
    )
    
    try:
        if args.command == "check":
            # 环境检查
            success = kg_system.check_environment()
            sys.exit(0 if success else 1)
            
        elif args.command == "build":
            # 构建知识图谱
            success = kg_system.build_knowledge_graph(clear_existing=args.clear)
            sys.exit(0 if success else 1)
            
        elif args.command == "test":
            # 测试功能
            query_success = kg_system.test_queries()
            llm_success = kg_system.test_llm_integration()
            success = query_success and llm_success
            sys.exit(0 if success else 1)
            
        elif args.command == "query":
            # 测试查询
            success = kg_system.test_queries()
            sys.exit(0 if success else 1)
            
        elif args.command == "interactive":
            # 交互式查询
            kg_system.interactive_query()
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
