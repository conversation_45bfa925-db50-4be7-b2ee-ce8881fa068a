# 电影从业者知识图谱项目

基于 IMDb `name.basics.tsv` 数据集构建的电影从业者知识图谱，支持自然语言查询。

## 项目结构

```
kg/
├── README.md                           # 项目说明文档
├── docker-compose.yml                  # Neo4j Docker配置
├── start_neo4j.sh                     # Neo4j启动脚本
├── stop_neo4j.sh                      # Neo4j停止脚本
├── knowledge_graph_build_guide_v2.md  # 详细构建指南
├── data_processor.py                  # 数据预处理模块
├── kg_builder.py                      # 知识图谱构建模块
├── query_interface.py                 # 查询接口模块
├── llm_integration.py                 # LLM集成模块
├── main.py                            # 主程序入口
├── requirements.txt                   # Python依赖
└── name.basics.tsv                    # IMDb数据文件（需要下载）
```

## 快速开始

### 1. 环境准备

1. **启动Docker Desktop**
   - 在macOS上启动Docker Desktop应用程序
   - 确保Docker正在运行

2. **启动Neo4j数据库**
   ```bash
   cd kg
   ./start_neo4j.sh
   ```

3. **访问Neo4j Browser**
   - 打开浏览器访问: http://localhost:7475
   - 用户名: `neo4j`
   - 密码: `kg_password_123`

### 2. 数据准备

1. **下载IMDb数据（可选）**
   ```bash
   # 下载name.basics.tsv文件到kg目录
   wget https://datasets.imdbws.com/name.basics.tsv.gz
   gunzip name.basics.tsv.gz
   ```

   **注意**: 如果没有真实数据文件，系统会自动创建示例数据用于测试。

2. **安装Python依赖**
   ```bash
   conda activate aideep2
   pip install -r requirements.txt
   ```

### 3. 系统检查

```bash
# 检查环境和依赖
python main.py check
```

### 4. 构建知识图谱

```bash
# 构建知识图谱（使用示例数据）
python main.py build --clear

# 或者使用真实数据文件
python main.py build --data-file name.basics.tsv --clear
```

### 5. 测试系统功能

```bash
# 运行所有测试
python test_kg_system.py

# 测试查询功能
python main.py query

# 测试完整功能
python main.py test
```

### 6. 交互式查询

```bash
# 启动交互式问答系统
python main.py interactive
```

### 7. 编程接口使用

```python
from query_interface import KGQueryInterface
from llm_integration import KGLLMInterface

# 基础查询接口
query_interface = KGQueryInterface()
query_interface.connect()

# 查询Tom Hanks的职业
result = query_interface.get_person_professions("Tom Hanks")
print(result)

# 自然语言查询接口
llm_interface = KGLLMInterface()
llm_interface.connect()

# 自然语言查询
result = llm_interface.natural_language_query("Tom Hanks有哪些职业？")
print(result["answer"])
```

## 核心功能

### 图谱Schema

- **节点类型**:
  - `Person`: 电影从业者 `{nconst, primaryName, birthYear}`
  - `Profession`: 职业身份 `{name}`

- **关系类型**:
  - `HAS_PROFESSION`: 人员与职业的关系

### 查询能力

1. **查询特定人物的职业身份**
2. **查询特定职业的从业者列表**
3. **查询多重身份的从业者**
4. **自然语言查询（通过LLM集成）**

## 配置说明

### Neo4j配置

- **Web界面端口**: 7475 (避免与其他Neo4j实例冲突)
- **Bolt连接端口**: 7688
- **用户名/密码**: neo4j/kg_password_123
- **数据持久化**: 本地目录映射

### 内存配置

- **堆内存**: 512MB - 2GB
- **页缓存**: 1GB
- **适合中等规模数据集**

## 故障排除

### Docker相关问题

1. **Docker未运行**
   ```bash
   # 启动Docker Desktop，然后重新运行
   ./start_neo4j.sh
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :7475
   lsof -i :7688
   ```

3. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs
   ```

### 数据相关问题

1. **数据文件缺失**
   - 确保 `name.basics.tsv` 文件在kg目录下
   - 检查文件格式和编码

2. **内存不足**
   - 调整docker-compose.yml中的内存配置
   - 考虑处理数据子集

## 停止服务

```bash
./stop_neo4j.sh
```

## 项目完成状态

✅ **已完成的功能**:
1. **环境配置**: Docker + Neo4j 完整部署方案
2. **数据处理**: 完整的数据预处理和清洗流程
3. **知识图谱构建**: Person和Profession节点及关系创建
4. **查询接口**: 丰富的Cypher查询功能
5. **LLM集成**: 自然语言到Cypher的转换
6. **系统集成**: 统一的命令行接口和交互式查询
7. **测试覆盖**: 完整的单元测试和集成测试

🧪 **测试结果**:
- 所有11个测试用例通过
- 数据预处理功能正常
- 模拟查询功能正常
- LLM集成功能正常（模拟模式）

## 使用说明

### 当前可用功能
1. **数据预处理**: 自动创建示例数据，支持真实IMDb数据
2. **知识图谱构建**: 完整的图谱构建流程（需要Neo4j运行）
3. **查询功能**: 支持人物职业查询、职业从业者查询等
4. **自然语言查询**: 支持中文问答（需要API密钥或使用模拟模式）

### 环境要求
- Python 3.8+（已在Python 3.13测试）
- Docker Desktop
- 8GB+ 内存推荐

## 下一步计划

1. **数据扩展**: 集成更多IMDb数据集（电影、演员关系等）
2. **查询优化**: 添加更多复杂查询模式和性能优化
3. **Web界面**: 构建用户友好的Web查询界面
4. **API服务**: 提供RESTful API接口
5. **可视化**: 添加图谱可视化功能
