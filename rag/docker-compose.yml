version: '3.8'

services:
  neo4j:
    image: neo4j:5.15-community
    container_name: aiops-neo4j
    ports:
      - "7474:7474"  # HTTP Web界面
      - "7687:7687"  # Bolt协议
    environment:
      # 认证设置
      - NEO4J_AUTH=neo4j/aiops123456
      
      # 插件配置
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      
      # 内存配置
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      
      # 性能优化
      - NEO4J_dbms_default__listen__address=0.0.0.0
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
      
      # 日志配置
      - NEO4J_dbms_logs_debug_level=INFO
      
    volumes:
      # 数据持久化
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
      
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "aiops123456", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local

networks:
  default:
    name: aiops-network
