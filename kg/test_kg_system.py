#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱系统测试模块
验证各个模块的功能正确性
"""

import unittest
import logging
import tempfile
import os
from pathlib import Path

from data_processor import DataProcessor
from kg_builder import KnowledgeGraphBuilder
from query_interface import KG<PERSON>ueryInterface
from llm_integration import KGLLMInterface

# 配置测试日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestDataProcessor(unittest.TestCase):
    """测试数据预处理模块"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = DataProcessor("test_data.tsv")
    
    def test_create_sample_data(self):
        """测试示例数据创建"""
        # 使用不存在的文件名，触发示例数据创建
        processor = DataProcessor("non_existent_file.tsv")
        success = processor.load_data()
        
        self.assertTrue(success)
        self.assertIsNotNone(processor.df)
        self.assertGreater(len(processor.df), 0)
        logger.info("✅ 示例数据创建测试通过")
    
    def test_data_cleaning(self):
        """测试数据清洗"""
        processor = DataProcessor("non_existent_file.tsv")
        processor.load_data()
        
        success = processor.clean_data()
        self.assertTrue(success)
        
        # 检查清洗后的数据
        df = processor.df
        self.assertIsNotNone(df)
        self.assertFalse(df['primaryName'].isnull().any())
        self.assertFalse(df['primaryProfession'].isnull().any())
        self.assertTrue(df['birthYear'].dtype in ['int64', 'int32'])
        logger.info("✅ 数据清洗测试通过")
    
    def test_profession_processing(self):
        """测试职业处理"""
        processor = DataProcessor("non_existent_file.tsv")
        processor.load_data()
        processor.clean_data()
        
        success = processor.process_professions()
        self.assertTrue(success)
        
        # 检查职业处理结果
        processed_data = processor.get_processed_data()
        self.assertIsNotNone(processed_data)
        self.assertIn('professions', processed_data.columns)
        
        # 检查职业列表格式
        first_row = processed_data.iloc[0]
        self.assertIsInstance(first_row['professions'], list)
        self.assertGreater(len(first_row['professions']), 0)
        logger.info("✅ 职业处理测试通过")
    
    def test_statistics(self):
        """测试统计信息"""
        processor = DataProcessor("non_existent_file.tsv")
        processor.load_data()
        processor.clean_data()
        processor.process_professions()
        
        stats = processor.get_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_records', stats)
        self.assertIn('unique_persons', stats)
        self.assertIn('birth_year_range', stats)
        self.assertIn('profession_stats', stats)
        logger.info("✅ 统计信息测试通过")


class TestKGBuilder(unittest.TestCase):
    """测试知识图谱构建模块"""
    
    def setUp(self):
        """测试前准备"""
        self.builder = KnowledgeGraphBuilder()
    
    def test_connection_handling(self):
        """测试连接处理"""
        # 测试连接失败的情况（Neo4j未运行）
        success = self.builder.connect()
        # 由于Neo4j可能未运行，这里不强制要求连接成功
        logger.info(f"Neo4j连接测试: {'成功' if success else '失败（可能是Neo4j未运行）'}")
        
        # 测试关闭连接
        self.builder.close()
        logger.info("✅ 连接处理测试通过")
    
    def test_cypher_query_generation(self):
        """测试Cypher查询生成"""
        # 这里测试查询语句的构建逻辑
        # 由于需要数据库连接，这里主要测试方法存在性
        self.assertTrue(hasattr(self.builder, 'create_person_node'))
        self.assertTrue(hasattr(self.builder, 'create_profession_and_relationship'))
        self.assertTrue(hasattr(self.builder, 'build_knowledge_graph'))
        logger.info("✅ Cypher查询生成测试通过")


class TestQueryInterface(unittest.TestCase):
    """测试查询接口模块"""
    
    def setUp(self):
        """测试前准备"""
        self.query_interface = KGQueryInterface()
    
    def test_query_methods(self):
        """测试查询方法"""
        # 测试方法存在性和基本功能
        self.assertTrue(hasattr(self.query_interface, 'get_person_professions'))
        self.assertTrue(hasattr(self.query_interface, 'get_people_by_profession'))
        self.assertTrue(hasattr(self.query_interface, 'get_people_with_multiple_professions'))
        self.assertTrue(hasattr(self.query_interface, 'get_profession_statistics'))
        self.assertTrue(hasattr(self.query_interface, 'search_people_by_name_pattern'))
        self.assertTrue(hasattr(self.query_interface, 'execute_custom_query'))
        logger.info("✅ 查询方法测试通过")
    
    def test_connection_error_handling(self):
        """测试连接错误处理"""
        # 在未连接状态下测试查询
        result = self.query_interface.get_person_professions("Test Person")
        self.assertIn("error", result)
        self.assertEqual(result["error"], "请先连接数据库")
        logger.info("✅ 连接错误处理测试通过")


class TestLLMIntegration(unittest.TestCase):
    """测试LLM集成模块"""
    
    def setUp(self):
        """测试前准备"""
        self.llm_interface = KGLLMInterface()
    
    def test_mock_cypher_generation(self):
        """测试模拟Cypher生成"""
        # 测试模拟模式下的查询生成
        test_queries = [
            "Tom Hanks有哪些职业？",
            "谁是演员？",
            "哪些人既是演员又是导演？"
        ]
        
        for query in test_queries:
            cypher = self.llm_interface._mock_cypher_generation(query)
            self.assertIsInstance(cypher, str)
            self.assertGreater(len(cypher), 0)
            # 检查是否包含基本的Cypher关键词
            self.assertTrue(any(keyword in cypher.upper() for keyword in ['MATCH', 'RETURN']))
        
        logger.info("✅ 模拟Cypher生成测试通过")
    
    def test_result_formatting(self):
        """测试结果格式化"""
        # 测试不同类型的结果格式化
        test_results = [
            {"success": False, "message": "未找到"},
            {"success": True, "person": {"name": "Tom Hanks", "professions": ["actor", "producer"]}},
            {"success": True, "people": [{"name": "Actor 1"}, {"name": "Actor 2"}], "profession": "actor"}
        ]
        
        for result in test_results:
            formatted = self.llm_interface._format_cypher_result(result, "测试查询")
            self.assertIsInstance(formatted, str)
            self.assertGreater(len(formatted), 0)
        
        logger.info("✅ 结果格式化测试通过")


class TestSystemIntegration(unittest.TestCase):
    """测试系统集成"""
    
    def test_full_pipeline_simulation(self):
        """测试完整流程模拟"""
        logger.info("开始完整流程模拟测试...")
        
        # 1. 数据预处理
        processor = DataProcessor("non_existent_file.tsv")
        self.assertTrue(processor.load_data())
        self.assertTrue(processor.clean_data())
        self.assertTrue(processor.process_professions())
        
        processed_data = processor.get_processed_data()
        self.assertIsNotNone(processed_data)
        logger.info("✅ 数据预处理完成")
        
        # 2. 知识图谱构建（模拟）
        builder = KnowledgeGraphBuilder()
        # 由于可能没有Neo4j连接，这里只测试初始化
        self.assertIsNotNone(builder)
        logger.info("✅ 知识图谱构建器初始化完成")
        
        # 3. 查询接口（模拟）
        query_interface = KGQueryInterface()
        self.assertIsNotNone(query_interface)
        logger.info("✅ 查询接口初始化完成")
        
        # 4. LLM集成（模拟）
        llm_interface = KGLLMInterface()
        self.assertIsNotNone(llm_interface)
        
        # 测试模拟查询
        result = llm_interface.natural_language_query("Tom Hanks有哪些职业？")
        self.assertIsInstance(result, dict)
        self.assertIn("question", result)
        logger.info("✅ LLM集成测试完成")
        
        logger.info("✅ 完整流程模拟测试通过")


def run_tests():
    """运行所有测试"""
    logger.info("=== 开始知识图谱系统测试 ===")

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试用例
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataProcessor))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestKGBuilder))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestQueryInterface))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestLLMIntegration))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSystemIntegration))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    logger.info("=== 测试结果汇总 ===")
    logger.info(f"运行测试: {result.testsRun}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    if result.failures:
        logger.error("失败的测试:")
        for test, traceback in result.failures:
            logger.error(f"  - {test}: {traceback}")
    
    if result.errors:
        logger.error("错误的测试:")
        for test, traceback in result.errors:
            logger.error(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    logger.info(f"测试结果: {'✅ 全部通过' if success else '❌ 存在失败'}")
    
    return success


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
