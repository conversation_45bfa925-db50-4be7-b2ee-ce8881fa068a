#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱项目演示脚本
展示项目的主要功能和使用方法
"""

import logging
import json
from pathlib import Path

from data_processor import DataProcessor
from query_interface import KGQueryInterface
from llm_integration import KGLLMInterface

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_data_processing():
    """演示数据预处理功能"""
    print("\n" + "="*60)
    print("🔄 数据预处理演示")
    print("="*60)
    
    # 初始化数据处理器
    processor = DataProcessor("demo_data.tsv")  # 使用不存在的文件名触发示例数据创建
    
    # 加载数据
    print("\n1. 加载数据...")
    if processor.load_data():
        print("✅ 数据加载成功")
        print(f"   原始数据量: {len(processor.df)} 条记录")
    else:
        print("❌ 数据加载失败")
        return
    
    # 数据清洗
    print("\n2. 数据清洗...")
    if processor.clean_data():
        print("✅ 数据清洗成功")
        print(f"   清洗后数据量: {len(processor.df)} 条记录")
    else:
        print("❌ 数据清洗失败")
        return
    
    # 职业处理
    print("\n3. 职业处理...")
    if processor.process_professions():
        print("✅ 职业处理成功")
        processed_data = processor.get_processed_data()
        print(f"   处理后数据量: {len(processed_data)} 条记录")
    else:
        print("❌ 职业处理失败")
        return
    
    # 显示统计信息
    print("\n4. 数据统计信息:")
    stats = processor.get_statistics()
    print(f"   总记录数: {stats['total_records']}")
    print(f"   唯一人员数: {stats['unique_persons']}")
    print(f"   出生年份范围: {stats['birth_year_range']['min']} - {stats['birth_year_range']['max']}")
    print(f"   职业类型数: {stats['profession_stats']['total_professions']}")
    
    print("\n   热门职业:")
    for prof, count in list(stats['profession_stats']['top_professions'].items())[:5]:
        print(f"     {prof}: {count} 人")
    
    # 显示示例数据
    print("\n5. 示例数据:")
    sample_data = processed_data.head(3)
    for _, row in sample_data.iterrows():
        print(f"   {row['primaryName']} ({row['birthYear']}) - {', '.join(row['professions'])}")


def demo_query_interface():
    """演示查询接口功能"""
    print("\n" + "="*60)
    print("🔍 查询接口演示")
    print("="*60)
    
    # 初始化查询接口
    query_interface = KGQueryInterface()
    
    print("\n尝试连接Neo4j数据库...")
    if not query_interface.connect():
        print("⚠️  Neo4j数据库未运行，将演示查询接口的错误处理功能")
        
        # 演示错误处理
        print("\n1. 测试连接错误处理:")
        result = query_interface.get_person_professions("Tom Hanks")
        print(f"   查询结果: {result}")
        
        print("\n💡 要完整体验查询功能，请:")
        print("   1. 启动Docker Desktop")
        print("   2. 运行: ./start_neo4j.sh")
        print("   3. 运行: python main.py build --clear")
        print("   4. 重新运行此演示")
        
        query_interface.close()
        return
    
    print("✅ Neo4j连接成功!")
    
    # 演示各种查询功能
    test_queries = [
        ("查询Tom Hanks的职业", lambda: query_interface.get_person_professions("Tom Hanks")),
        ("查询所有演员", lambda: query_interface.get_people_by_profession("actor", 5)),
        ("查询既是演员又是导演的人", lambda: query_interface.get_people_with_multiple_professions(["actor", "director"], 3)),
        ("搜索包含Tom的人物", lambda: query_interface.search_people_by_name_pattern("Tom", 3)),
        ("获取职业统计信息", lambda: query_interface.get_profession_statistics())
    ]
    
    for i, (description, query_func) in enumerate(test_queries, 1):
        print(f"\n{i}. {description}:")
        try:
            result = query_func()
            if result.get("success", False):
                print("   ✅ 查询成功")
                # 简化输出
                if "person" in result:
                    person = result["person"]
                    print(f"      {person['name']}: {', '.join(person['professions'])}")
                elif "people" in result:
                    people = result["people"][:3]  # 只显示前3个
                    for person in people:
                        print(f"      {person['name']}")
                elif "professionDistribution" in result:
                    top_profs = result["professionDistribution"][:3]
                    for prof in top_profs:
                        print(f"      {prof['profession']}: {prof['count']} 人")
            else:
                print(f"   ⚠️  {result.get('message', '查询无结果')}")
        except Exception as e:
            print(f"   ❌ 查询异常: {e}")
    
    query_interface.close()


def demo_llm_integration():
    """演示LLM集成功能"""
    print("\n" + "="*60)
    print("🤖 LLM集成演示")
    print("="*60)
    
    # 初始化LLM接口
    llm_interface = KGLLMInterface()
    
    print("\n尝试连接Neo4j数据库...")
    if not llm_interface.connect():
        print("⚠️  Neo4j数据库未运行，将演示模拟模式功能")
    else:
        print("✅ Neo4j连接成功!")
    
    # 测试自然语言查询
    test_questions = [
        "Tom Hanks有哪些职业？",
        "谁是演员？",
        "哪些人既是演员又是导演？",
        "有多少种职业？"
    ]
    
    print(f"\n🧠 LLM模式: {'API模式' if not llm_interface.use_mock else '模拟模式'}")
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. 问题: {question}")
        try:
            result = llm_interface.natural_language_query(question)
            
            if result.get("success", False):
                print(f"   🤖 回答: {result['answer']}")
                print(f"   🔍 生成的Cypher: {result['cypher_query']}")
            else:
                print(f"   ❌ 查询失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"   ❌ 查询异常: {e}")
    
    llm_interface.close()


def demo_system_integration():
    """演示系统集成功能"""
    print("\n" + "="*60)
    print("🔧 系统集成演示")
    print("="*60)
    
    print("\n1. 项目结构:")
    kg_dir = Path(".")
    important_files = [
        "README.md", "main.py", "requirements.txt",
        "data_processor.py", "kg_builder.py", "query_interface.py", 
        "llm_integration.py", "test_kg_system.py",
        "docker-compose.yml", "start_neo4j.sh", "stop_neo4j.sh"
    ]
    
    for file in important_files:
        if (kg_dir / file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
    
    print("\n2. 主要命令:")
    commands = [
        ("python main.py check", "环境检查"),
        ("python main.py build --clear", "构建知识图谱"),
        ("python main.py query", "测试查询功能"),
        ("python main.py test", "完整功能测试"),
        ("python main.py interactive", "交互式查询"),
        ("python test_kg_system.py", "运行单元测试")
    ]
    
    for cmd, desc in commands:
        print(f"   {cmd:<35} # {desc}")
    
    print("\n3. 功能模块:")
    modules = [
        ("data_processor.py", "数据预处理", "✅"),
        ("kg_builder.py", "知识图谱构建", "✅"),
        ("query_interface.py", "查询接口", "✅"),
        ("llm_integration.py", "LLM集成", "✅"),
        ("main.py", "系统集成", "✅"),
        ("test_kg_system.py", "测试覆盖", "✅")
    ]
    
    for module, desc, status in modules:
        print(f"   {status} {module:<25} # {desc}")


def main():
    """主演示函数"""
    print("🎬 知识图谱项目演示")
    print("基于IMDb数据的电影从业者知识图谱")
    print("支持自然语言查询的智能问答系统")
    
    try:
        # 1. 数据处理演示
        demo_data_processing()
        
        # 2. 查询接口演示
        demo_query_interface()
        
        # 3. LLM集成演示
        demo_llm_integration()
        
        # 4. 系统集成演示
        demo_system_integration()
        
        print("\n" + "="*60)
        print("🎉 演示完成!")
        print("="*60)
        
        print("\n📖 下一步:")
        print("1. 启动Neo4j: ./start_neo4j.sh")
        print("2. 构建图谱: python main.py build --clear")
        print("3. 交互查询: python main.py interactive")
        print("4. 查看文档: cat README.md")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
