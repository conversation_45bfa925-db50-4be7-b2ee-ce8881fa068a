# RAG个人知识库环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# 通义百炼API配置
# =============================================================================
# 通义百炼API密钥（必填）
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# API基础URL（可选，默认使用官方地址）
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# =============================================================================
# 运行环境配置
# =============================================================================
# 运行环境：development/production
ENVIRONMENT=development

# 调试模式：true/false
DEBUG=true

# 日志级别：DEBUG/INFO/WARNING/ERROR
LOG_LEVEL=INFO

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL配置（可选）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=rag_db
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=rag_password

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=rag_redis_pass
REDIS_DB=0

# =============================================================================
# 向量数据库配置
# =============================================================================
# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION=knowledge_base

# FAISS配置
FAISS_INDEX_PATH=./vectorstore/faiss.index

# =============================================================================
# 嵌入模型配置
# =============================================================================
# 嵌入模型名称
EMBEDDING_MODEL=BAAI/bge-small-zh-v1.5

# 计算设备：cpu/mps/cuda
EMBEDDING_DEVICE=mps

# 最大文本长度
EMBEDDING_MAX_LENGTH=512

# 批处理大小
EMBEDDING_BATCH_SIZE=32

# =============================================================================
# LLM配置
# =============================================================================
# 模型名称
LLM_MODEL=qwen-max

# 生成参数
LLM_TEMPERATURE=0.7
LLM_TOP_P=0.9
LLM_MAX_TOKENS=2048

# 请求配置
LLM_TIMEOUT=60
LLM_MAX_RETRIES=3

# =============================================================================
# 文本分割配置
# =============================================================================
# 文本块大小
CHUNK_SIZE=500

# 文本块重叠
CHUNK_OVERLAP=50

# =============================================================================
# API服务配置
# =============================================================================
# 服务主机地址
API_HOST=0.0.0.0

# 服务端口
API_PORT=8000

# CORS允许的源
CORS_ORIGINS=["*"]

# =============================================================================
# 文件存储配置
# =============================================================================
# MinIO配置（可选）
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=rag-documents

# =============================================================================
# 监控和日志配置
# =============================================================================
# 日志目录
LOG_DIR=./logs

# 日志文件最大大小
LOG_MAX_SIZE=10MB

# 日志保留时间
LOG_RETENTION=30 days

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥（如果启用认证）
JWT_SECRET_KEY=your_jwt_secret_key_here

# API密钥（如果启用API认证）
API_SECRET_KEY=your_api_secret_key_here

# =============================================================================
# 性能配置
# =============================================================================
# 工作进程数
WORKERS=1

# 最大并发连接数
MAX_CONNECTIONS=100

# 请求超时时间
REQUEST_TIMEOUT=300

# =============================================================================
# 特性开关
# =============================================================================
# 是否启用混合检索
ENABLE_HYBRID_SEARCH=true

# 是否启用重排序
ENABLE_RERANKING=true

# 是否启用查询扩展
ENABLE_QUERY_EXPANSION=false

# 是否启用缓存
ENABLE_CACHE=true

# =============================================================================
# 开发配置
# =============================================================================
# 是否启用热重载
RELOAD=true

# 是否启用API文档
ENABLE_DOCS=true

# 是否启用性能监控
ENABLE_PROFILING=false
