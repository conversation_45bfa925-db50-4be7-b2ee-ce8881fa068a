#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cypher查询接口模块
实现基础的Cypher查询功能，包括查询特定人物身份、查询特定职业的从业者等核心查询需求
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, AuthError
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KGQueryInterface:
    """知识图谱查询接口"""
    
    def __init__(self, uri: str = "bolt://localhost:7688", 
                 username: str = "neo4j", 
                 password: str = "kg_password_123",
                 database: str = "neo4j"):
        """
        初始化查询接口
        
        Args:
            uri: Neo4j连接URI
            username: 用户名
            password: 密码
            database: 数据库名称
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.driver = None
        
    def connect(self) -> bool:
        """
        连接到Neo4j数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"正在连接到Neo4j数据库: {self.uri}")
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.username, self.password)
            )
            
            # 测试连接
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    logger.info("Neo4j连接成功!")
                    return True
                    
        except ServiceUnavailable as e:
            logger.error(f"Neo4j服务不可用: {e}")
        except AuthError as e:
            logger.error(f"认证失败: {e}")
        except Exception as e:
            logger.error(f"连接失败: {e}")
            
        return False
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logger.info("数据库连接已关闭")
    
    def get_person_professions(self, person_name: str) -> Dict[str, Any]:
        """
        查询特定人物的所有职业身份
        
        Args:
            person_name: 人物姓名
            
        Returns:
            包含查询结果的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MATCH (p:Person {primaryName: $person_name})-[:HAS_PROFESSION]->(prof:Profession)
                RETURN p.primaryName AS name, 
                       p.birthYear AS birthYear,
                       p.nconst AS nconst,
                       collect(prof.name) AS professions
                """
                
                result = session.run(query, person_name=person_name)
                record = result.single()
                
                if record:
                    return {
                        "success": True,
                        "person": {
                            "name": record["name"],
                            "birthYear": record["birthYear"],
                            "nconst": record["nconst"],
                            "professions": record["professions"]
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"未找到名为 '{person_name}' 的人物"
                    }
                    
        except Exception as e:
            logger.error(f"查询人物职业失败: {e}")
            return {"error": str(e)}
    
    def get_people_by_profession(self, profession_name: str, limit: int = 20) -> Dict[str, Any]:
        """
        查询拥有特定职业的从业者列表
        
        Args:
            profession_name: 职业名称
            limit: 返回结果数量限制
            
        Returns:
            包含查询结果的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MATCH (prof:Profession {name: $profession_name})<-[:HAS_PROFESSION]-(p:Person)
                RETURN p.primaryName AS name, 
                       p.birthYear AS birthYear,
                       p.nconst AS nconst
                ORDER BY p.primaryName
                LIMIT $limit
                """
                
                result = session.run(query, profession_name=profession_name, limit=limit)
                people = []
                
                for record in result:
                    people.append({
                        "name": record["name"],
                        "birthYear": record["birthYear"],
                        "nconst": record["nconst"]
                    })
                
                if people:
                    return {
                        "success": True,
                        "profession": profession_name,
                        "count": len(people),
                        "people": people
                    }
                else:
                    return {
                        "success": False,
                        "message": f"未找到职业为 '{profession_name}' 的从业者"
                    }
                    
        except Exception as e:
            logger.error(f"查询职业从业者失败: {e}")
            return {"error": str(e)}
    
    def get_people_with_multiple_professions(self, professions: List[str], limit: int = 20) -> Dict[str, Any]:
        """
        查询同时拥有多个职业身份的从业者
        
        Args:
            professions: 职业名称列表
            limit: 返回结果数量限制
            
        Returns:
            包含查询结果的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        if len(professions) < 2:
            return {"error": "至少需要提供2个职业"}
            
        try:
            with self.driver.session(database=self.database) as session:
                # 构建动态查询
                match_clauses = []
                for i, prof in enumerate(professions):
                    match_clauses.append(f"MATCH (p)-[:HAS_PROFESSION]->(:Profession {{name: $prof{i}}})")
                
                query = f"""
                {' '.join(match_clauses)}
                WITH p
                MATCH (p)-[:HAS_PROFESSION]->(prof:Profession)
                RETURN p.primaryName AS name, 
                       p.birthYear AS birthYear,
                       p.nconst AS nconst,
                       collect(prof.name) AS allProfessions
                ORDER BY p.primaryName
                LIMIT $limit
                """
                
                # 构建参数字典
                params = {f"prof{i}": prof for i, prof in enumerate(professions)}
                params["limit"] = limit
                
                result = session.run(query, **params)
                people = []
                
                for record in result:
                    people.append({
                        "name": record["name"],
                        "birthYear": record["birthYear"],
                        "nconst": record["nconst"],
                        "allProfessions": record["allProfessions"]
                    })
                
                if people:
                    return {
                        "success": True,
                        "requiredProfessions": professions,
                        "count": len(people),
                        "people": people
                    }
                else:
                    return {
                        "success": False,
                        "message": f"未找到同时拥有 {', '.join(professions)} 职业的从业者"
                    }
                    
        except Exception as e:
            logger.error(f"查询多重职业从业者失败: {e}")
            return {"error": str(e)}
    
    def get_profession_statistics(self) -> Dict[str, Any]:
        """
        获取职业统计信息
        
        Returns:
            包含统计信息的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        try:
            with self.driver.session(database=self.database) as session:
                # 获取职业分布
                query = """
                MATCH (prof:Profession)<-[:HAS_PROFESSION]-()
                RETURN prof.name AS profession, count(*) AS count
                ORDER BY count DESC
                """
                
                result = session.run(query)
                profession_stats = []
                
                for record in result:
                    profession_stats.append({
                        "profession": record["profession"],
                        "count": record["count"]
                    })
                
                # 获取总体统计
                total_people = session.run("MATCH (p:Person) RETURN count(p) AS count").single()["count"]
                total_professions = session.run("MATCH (prof:Profession) RETURN count(prof) AS count").single()["count"]
                total_relationships = session.run("MATCH ()-[r:HAS_PROFESSION]->() RETURN count(r) AS count").single()["count"]
                
                return {
                    "success": True,
                    "totalPeople": total_people,
                    "totalProfessions": total_professions,
                    "totalRelationships": total_relationships,
                    "professionDistribution": profession_stats
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def search_people_by_name_pattern(self, name_pattern: str, limit: int = 20) -> Dict[str, Any]:
        """
        根据姓名模式搜索人物
        
        Args:
            name_pattern: 姓名模式（支持部分匹配）
            limit: 返回结果数量限制
            
        Returns:
            包含查询结果的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MATCH (p:Person)
                WHERE p.primaryName CONTAINS $name_pattern
                OPTIONAL MATCH (p)-[:HAS_PROFESSION]->(prof:Profession)
                RETURN p.primaryName AS name, 
                       p.birthYear AS birthYear,
                       p.nconst AS nconst,
                       collect(prof.name) AS professions
                ORDER BY p.primaryName
                LIMIT $limit
                """
                
                result = session.run(query, name_pattern=name_pattern, limit=limit)
                people = []
                
                for record in result:
                    people.append({
                        "name": record["name"],
                        "birthYear": record["birthYear"],
                        "nconst": record["nconst"],
                        "professions": record["professions"] or []
                    })
                
                if people:
                    return {
                        "success": True,
                        "searchPattern": name_pattern,
                        "count": len(people),
                        "people": people
                    }
                else:
                    return {
                        "success": False,
                        "message": f"未找到包含 '{name_pattern}' 的人物"
                    }
                    
        except Exception as e:
            logger.error(f"搜索人物失败: {e}")
            return {"error": str(e)}
    
    def execute_custom_query(self, cypher_query: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行自定义Cypher查询
        
        Args:
            cypher_query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            包含查询结果的字典
        """
        if not self.driver:
            return {"error": "请先连接数据库"}
            
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(cypher_query, parameters or {})
                
                records = []
                for record in result:
                    records.append(dict(record))
                
                return {
                    "success": True,
                    "query": cypher_query,
                    "count": len(records),
                    "results": records
                }
                
        except Exception as e:
            logger.error(f"执行自定义查询失败: {e}")
            return {"error": str(e)}


def main():
    """主函数，演示查询接口功能"""
    logger.info("=== Cypher查询接口模块测试 ===")
    
    # 初始化查询接口
    query_interface = KGQueryInterface()
    
    try:
        # 连接数据库
        if not query_interface.connect():
            logger.error("无法连接到数据库，请检查Neo4j是否正在运行")
            return
        
        # 测试各种查询功能
        logger.info("\n1. 查询Tom Hanks的职业:")
        result = query_interface.get_person_professions("Tom Hanks")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("\n2. 查询所有演员:")
        result = query_interface.get_people_by_profession("actor", limit=5)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("\n3. 查询既是演员又是导演的人:")
        result = query_interface.get_people_with_multiple_professions(["actor", "director"], limit=5)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("\n4. 获取职业统计信息:")
        result = query_interface.get_profession_statistics()
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        logger.info("\n5. 搜索包含'Tom'的人物:")
        result = query_interface.search_people_by_name_pattern("Tom", limit=3)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    finally:
        # 关闭连接
        query_interface.close()


if __name__ == "__main__":
    main()
