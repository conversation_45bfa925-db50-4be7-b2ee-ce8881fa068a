#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM集成与问答系统模块
集成qwen-plus模型，使用LangChain实现Text-to-Cypher转换，构建自然语言问答接口
"""

import logging
import os
import json
from typing import Dict, List, Any, Optional
import dashscope
from dashscope import Generation
from query_interface import KGQueryInterface

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KGLLMInterface:
    """知识图谱LLM问答接口"""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model_name: str = "qwen-plus",
                 neo4j_uri: str = "bolt://localhost:7688",
                 neo4j_username: str = "neo4j",
                 neo4j_password: str = "kg_password_123"):
        """
        初始化LLM接口
        
        Args:
            api_key: 通义百炼API密钥
            model_name: 模型名称
            neo4j_uri: Neo4j连接URI
            neo4j_username: Neo4j用户名
            neo4j_password: Neo4j密码
        """
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        self.model_name = model_name
        
        # 初始化查询接口
        self.query_interface = KGQueryInterface(
            uri=neo4j_uri,
            username=neo4j_username,
            password=neo4j_password
        )
        
        # 设置API密钥
        if not self.api_key:
            logger.warning("API密钥未设置，将使用模拟模式")
            self.use_mock = True
        else:
            dashscope.api_key = self.api_key
            self.use_mock = False
        
        # 生成配置
        self.generation_config = {
            "model": self.model_name,
            "temperature": 0.1,  # 低温度确保生成稳定的Cypher查询
            "top_p": 0.9,
            "max_tokens": 1024,
        }
        
        # 知识图谱Schema信息
        self.schema_info = """
知识图谱Schema:
- 节点类型:
  * Person: 电影从业者 {nconst: string, primaryName: string, birthYear: integer}
  * Profession: 职业身份 {name: string}
- 关系类型:
  * HAS_PROFESSION: Person -> Profession (人员拥有某个职业身份)

示例查询:
1. 查询Tom Hanks的职业: MATCH (p:Person {primaryName: 'Tom Hanks'})-[:HAS_PROFESSION]->(prof:Profession) RETURN p.primaryName, collect(prof.name) as professions
2. 查询所有演员: MATCH (prof:Profession {name: 'actor'})<-[:HAS_PROFESSION]-(p:Person) RETURN p.primaryName LIMIT 10
3. 查询既是演员又是导演的人: MATCH (p:Person)-[:HAS_PROFESSION]->(:Profession {name: 'actor'}) MATCH (p)-[:HAS_PROFESSION]->(:Profession {name: 'director'}) RETURN p.primaryName
"""
        
        logger.info(f"LLM接口初始化完成 | 模型: {self.model_name} | 模拟模式: {self.use_mock}")
    
    def connect(self) -> bool:
        """连接到Neo4j数据库"""
        return self.query_interface.connect()
    
    def close(self):
        """关闭连接"""
        self.query_interface.close()
    
    def _generate_cypher_query(self, natural_language_query: str) -> str:
        """
        将自然语言查询转换为Cypher查询
        
        Args:
            natural_language_query: 自然语言查询
            
        Returns:
            生成的Cypher查询语句
        """
        if self.use_mock:
            return self._mock_cypher_generation(natural_language_query)
        
        system_prompt = f"""你是一个专业的Cypher查询生成器。根据用户的自然语言问题，生成对应的Cypher查询语句。

{self.schema_info}

要求:
1. 只返回Cypher查询语句，不要包含其他解释
2. 查询语句必须符合Neo4j Cypher语法
3. 使用MATCH、WHERE、RETURN等关键词
4. 对于人名查询，使用精确匹配
5. 对于职业查询，使用小写匹配
6. 适当使用LIMIT限制结果数量

用户问题: {natural_language_query}

Cypher查询:"""
        
        try:
            messages = [{"role": "user", "content": system_prompt}]
            
            response = Generation.call(
                **self.generation_config,
                messages=messages
            )
            
            if response.status_code == 200 and response.output:
                cypher_query = response.output.text.strip()
                # 清理可能的代码块标记
                cypher_query = cypher_query.replace("```cypher", "").replace("```", "").strip()
                logger.info(f"生成的Cypher查询: {cypher_query}")
                return cypher_query
            else:
                logger.error(f"Cypher生成失败: {response.message}")
                return ""
                
        except Exception as e:
            logger.error(f"Cypher生成异常: {e}")
            return ""
    
    def _mock_cypher_generation(self, natural_language_query: str) -> str:
        """
        模拟Cypher查询生成（用于没有API密钥的情况）
        
        Args:
            natural_language_query: 自然语言查询
            
        Returns:
            模拟生成的Cypher查询
        """
        query_lower = natural_language_query.lower()
        
        # 简单的规则匹配
        if "tom hanks" in query_lower:
            return "MATCH (p:Person {primaryName: 'Tom Hanks'})-[:HAS_PROFESSION]->(prof:Profession) RETURN p.primaryName, collect(prof.name) as professions"
        elif "morgan freeman" in query_lower:
            return "MATCH (p:Person {primaryName: 'Morgan Freeman'})-[:HAS_PROFESSION]->(prof:Profession) RETURN p.primaryName, collect(prof.name) as professions"
        elif "actor" in query_lower and "director" in query_lower:
            return "MATCH (p:Person)-[:HAS_PROFESSION]->(:Profession {name: 'actor'}) MATCH (p)-[:HAS_PROFESSION]->(:Profession {name: 'director'}) RETURN p.primaryName LIMIT 10"
        elif "actor" in query_lower:
            return "MATCH (prof:Profession {name: 'actor'})<-[:HAS_PROFESSION]-(p:Person) RETURN p.primaryName LIMIT 10"
        elif "director" in query_lower:
            return "MATCH (prof:Profession {name: 'director'})<-[:HAS_PROFESSION]-(p:Person) RETURN p.primaryName LIMIT 10"
        elif "producer" in query_lower:
            return "MATCH (prof:Profession {name: 'producer'})<-[:HAS_PROFESSION]-(p:Person) RETURN p.primaryName LIMIT 10"
        elif "profession" in query_lower or "职业" in query_lower:
            return "MATCH (prof:Profession)<-[:HAS_PROFESSION]-() RETURN prof.name, count(*) as count ORDER BY count DESC LIMIT 10"
        else:
            return "MATCH (p:Person) RETURN p.primaryName LIMIT 5"
    
    def _format_cypher_result(self, result: Dict[str, Any], original_query: str) -> str:
        """
        格式化Cypher查询结果为自然语言回答
        
        Args:
            result: 查询结果
            original_query: 原始自然语言查询
            
        Returns:
            格式化的自然语言回答
        """
        if not result.get("success", False) and "results" not in result:
            return f"抱歉，我无法找到相关信息。{result.get('message', '')}"
        
        if "results" in result:
            # 自定义查询结果
            results = result["results"]
            if not results:
                return "查询没有返回任何结果。"
            
            # 根据结果格式化回答
            if len(results) == 1 and "professions" in results[0]:
                # 单个人物的职业查询
                person_data = results[0]
                name = person_data.get("p.primaryName", "未知")
                professions = person_data.get("professions", [])
                if professions:
                    return f"{name}的职业包括：{', '.join(professions)}"
                else:
                    return f"没有找到{name}的职业信息。"
            
            elif all("p.primaryName" in r for r in results):
                # 人员列表查询
                names = [r["p.primaryName"] for r in results if "p.primaryName" in r]
                if len(names) > 10:
                    return f"找到{len(names)}个相关人员，前10个包括：{', '.join(names[:10])}"
                else:
                    return f"相关人员包括：{', '.join(names)}"
            
            elif all("prof.name" in r and "count" in r for r in results):
                # 职业统计查询
                prof_stats = [(r["prof.name"], r["count"]) for r in results]
                stats_text = "、".join([f"{prof}({count}人)" for prof, count in prof_stats[:5]])
                return f"主要职业分布：{stats_text}"
            
            else:
                # 通用格式化
                return f"查询结果：{json.dumps(results, ensure_ascii=False, indent=2)}"
        
        # 使用现有的查询接口结果格式
        if result.get("success"):
            if "person" in result:
                person = result["person"]
                name = person["name"]
                professions = person["professions"]
                return f"{name}的职业包括：{', '.join(professions)}"
            elif "people" in result:
                people = result["people"]
                profession = result.get("profession", "相关")
                names = [p["name"] for p in people[:10]]
                return f"{profession}职业的从业者包括：{', '.join(names)}"
        
        return "查询完成，但结果格式无法识别。"
    
    def natural_language_query(self, question: str) -> Dict[str, Any]:
        """
        自然语言查询接口
        
        Args:
            question: 自然语言问题
            
        Returns:
            包含回答和详细信息的字典
        """
        logger.info(f"收到自然语言查询: {question}")
        
        try:
            # 1. 生成Cypher查询
            cypher_query = self._generate_cypher_query(question)
            if not cypher_query:
                return {
                    "success": False,
                    "error": "无法生成Cypher查询",
                    "question": question
                }
            
            # 2. 执行Cypher查询
            query_result = self.query_interface.execute_custom_query(cypher_query)
            
            # 3. 格式化结果
            formatted_answer = self._format_cypher_result(query_result, question)
            
            return {
                "success": True,
                "question": question,
                "cypher_query": cypher_query,
                "raw_result": query_result,
                "answer": formatted_answer
            }
            
        except Exception as e:
            logger.error(f"自然语言查询失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "question": question
            }
    
    def batch_query(self, questions: List[str]) -> List[Dict[str, Any]]:
        """
        批量查询
        
        Args:
            questions: 问题列表
            
        Returns:
            查询结果列表
        """
        results = []
        for question in questions:
            result = self.natural_language_query(question)
            results.append(result)
        return results


def main():
    """主函数，演示LLM集成功能"""
    logger.info("=== LLM集成与问答系统测试 ===")
    
    # 初始化LLM接口
    llm_interface = KGLLMInterface()
    
    try:
        # 连接数据库
        if not llm_interface.connect():
            logger.error("无法连接到数据库，请检查Neo4j是否正在运行")
            return
        
        # 测试问题列表
        test_questions = [
            "Tom Hanks有哪些职业？",
            "谁是演员？",
            "哪些人既是演员又是导演？",
            "Morgan Freeman的职业是什么？",
            "有多少种职业？"
        ]
        
        logger.info("开始测试自然语言查询...")
        
        for i, question in enumerate(test_questions, 1):
            logger.info(f"\n=== 测试 {i}: {question} ===")
            
            result = llm_interface.natural_language_query(question)
            
            if result["success"]:
                print(f"问题: {result['question']}")
                print(f"生成的Cypher: {result['cypher_query']}")
                print(f"回答: {result['answer']}")
            else:
                print(f"查询失败: {result.get('error', '未知错误')}")
            
            print("-" * 50)
        
        logger.info("自然语言查询测试完成!")
        
    finally:
        # 关闭连接
        llm_interface.close()


if __name__ == "__main__":
    main()
