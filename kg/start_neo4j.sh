#!/bin/bash

# 知识图谱项目 Neo4j 启动脚本

echo "=== 知识图谱项目 Neo4j 启动脚本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker 未运行，请先启动 Docker"
    exit 1
fi

# 进入kg目录
cd "$(dirname "$0")"

# 创建必要的目录
echo "创建数据目录..."
mkdir -p neo4j_data neo4j_logs neo4j_import neo4j_plugins

# 启动Neo4j容器
echo "启动 Neo4j 容器..."
docker-compose up -d

# 等待容器启动
echo "等待 Neo4j 启动..."
sleep 10

# 检查容器状态
if docker ps | grep -q "kg_neo4j_server"; then
    echo "✅ Neo4j 容器启动成功!"
    echo ""
    echo "访问信息:"
    echo "- Neo4j Browser: http://localhost:7475"
    echo "- 用户名: neo4j"
    echo "- 密码: kg_password_123"
    echo "- Bolt连接: bolt://localhost:7688"
    echo ""
    echo "容器状态:"
    docker ps | grep kg_neo4j_server
else
    echo "❌ Neo4j 容器启动失败"
    echo "查看日志:"
    docker-compose logs
    exit 1
fi
